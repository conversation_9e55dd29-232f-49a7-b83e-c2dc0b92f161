import { useState, useRef, useEffect, useCallback, forwardRef } from "react";
import { ConditionalDisplay } from "../UI/ConditionalDisplay/ConditionalDisplay";

const VideoPlayer = forwardRef(
  (
    {
      src,
      type = "video/mp4",
      embed = false,
      width = "100%",
      height = "auto",
      autoplay = true,
      muted = true,
      controls = true,
      poster = null,
      fallbackText = "Your browser does not support video playback.",
      onError = null,
      onLoad = null,
      onVideoComplete = null, // Callback when video is played completely once
      className = "",
      style = {},
      azureStorageConfig = null, // { accountName, containerName, sasToken }
      alternativeSources = [], // Array of alternative video sources
      retryAttempts = 3,
      showLoadingSpinner = true,
    },
    ref
  ) => {
    const [videoError, setVideoError] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [currentSrcIndex, setCurrentSrcIndex] = useState(0);
    const [retryCount, setRetryCount] = useState(0);
    const [hasCompletedOnce, setHasCompletedOnce] = useState(false);

    const videoRef = useRef(null);

    // Detect video source type
    const detectVideoType = useCallback(
      (url) => {
        if (!url) return "unknown";

        const urlLower = url.toLowerCase();

        // YouTube detection
        if (urlLower.includes("youtube.com") || urlLower.includes("youtu.be")) {
          return "youtube";
        }

        // LinkedIn detection
        if (urlLower.includes("linkedin.com")) {
          return "linkedin";
        }

        // Azure Media Services detection
        if (
          urlLower.includes(".streaming.media.azure.net") ||
          urlLower.includes("azureedge.net") ||
          (azureStorageConfig &&
            urlLower.includes(azureStorageConfig.accountName))
        ) {
          return "azure";
        }

        // Direct video file detection
        if (urlLower.match(/\.(mp4|webm|ogg|avi|mov|wmv|flv|mkv)(\?|$)/)) {
          return "direct";
        }

        return "direct"; // Default to direct for unknown types
      },
      [azureStorageConfig]
    );

    // Convert YouTube URL to embed format
    const getYouTubeEmbedUrl = useCallback(
      (url) => {
        let videoId = "";

        if (url.includes("youtube.com/watch?v=")) {
          videoId = url.split("v=")[1].split("&")[0];
        } else if (url.includes("youtu.be/")) {
          videoId = url.split("youtu.be/")[1].split("?")[0];
        } else if (url.includes("youtube.com/embed/")) {
          return url; // Already in embed format
        }

        if (videoId) {
          return `https://www.youtube.com/embed/${videoId}?autoplay=${
            autoplay ? 1 : 0
          }&mute=${muted ? 1 : 0}`;
        }

        return url;
      },
      [autoplay, muted]
    );

    // Convert LinkedIn URL to embed format
    const getLinkedInEmbedUrl = useCallback((url) => {
      // LinkedIn video embed format
      if (url.includes("/posts/") && !url.includes("/embed/")) {
        const postId = url.split("/posts/")[1].split("/")[0];
        return `https://www.linkedin.com/embed/feed/update/urn:li:share:${postId}`;
      }
      return url;
    }, []);

    // Construct Azure Media Services URL with SAS token
    const getAzureMediaUrl = useCallback(
      (url) => {
        if (azureStorageConfig && azureStorageConfig.sasToken) {
          const separator = url.includes("?") ? "&" : "?";
          return `${url}${separator}${azureStorageConfig.sasToken}`;
        }
        return url;
      },
      [azureStorageConfig]
    );

    // Get processed source URL based on type
    const getProcessedSrc = useCallback(
      (url, type) => {
        switch (type) {
          case "youtube":
            return getYouTubeEmbedUrl(url);
          case "linkedin":
            return getLinkedInEmbedUrl(url);
          case "azure":
            return getAzureMediaUrl(url);
          default:
            return url;
        }
      },
      [getYouTubeEmbedUrl, getLinkedInEmbedUrl, getAzureMediaUrl]
    );

    // Initialize video and reset state when src changes
    useEffect(() => {
      if (src) {
        setVideoError(false);
        setCurrentSrcIndex(0);
        setRetryCount(0);
        setIsLoading(true);
        setHasCompletedOnce(false); // Reset completion state for new video
      }
    }, [src]);

    // Handle video load success
    const handleVideoLoad = useCallback(() => {
      setIsLoading(false);
      setVideoError(false);
      if (onLoad) onLoad();
    }, [onLoad]);

    // Handle video completion
    const handleVideoComplete = useCallback(() => {
      if (!hasCompletedOnce && onVideoComplete) {
        setHasCompletedOnce(true);
        onVideoComplete();
      }
    }, [hasCompletedOnce, onVideoComplete]);

    // Handle video error with retry logic
    const handleVideoError = useCallback(
      (error) => {
        console.error("Video error:", error);

        if (retryCount < retryAttempts) {
          // Retry with same source
          setRetryCount((prev) => prev + 1);
          setTimeout(() => {
            if (videoRef.current) {
              videoRef.current.load();
            }
          }, 1000);
          return;
        }

        // Try alternative sources
        if (currentSrcIndex < alternativeSources.length) {
          setCurrentSrcIndex((prev) => prev + 1);
          setRetryCount(0);
          return;
        }

        // All sources failed
        setVideoError(true);
        setIsLoading(false);
        if (onError) onError(error);
      },
      [
        retryCount,
        retryAttempts,
        currentSrcIndex,
        alternativeSources.length,
        onError,
      ]
    );

    // Get current video source
    const getCurrentSrc = useCallback(() => {
      if (currentSrcIndex === 0) {
        return src;
      } else if (currentSrcIndex <= alternativeSources.length) {
        return alternativeSources[currentSrcIndex - 1];
      }
      return src;
    }, [src, currentSrcIndex, alternativeSources]);

    // Loading spinner component
    const LoadingSpinner = () => (
      <div
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "200px",
          backgroundColor: "#f0f0f0",
          borderRadius: "8px",
        }}
      >
        <div
          style={{
            border: "4px solid #f3f3f3",
            borderTop: "4px solid #3498db",
            borderRadius: "50%",
            width: "40px",
            height: "40px",
            animation: "spin 2s linear infinite",
          }}
        ></div>
        <style jsx>{`
          @keyframes spin {
            0% {
              transform: rotate(0deg);
            }
            100% {
              transform: rotate(360deg);
            }
          }
        `}</style>
      </div>
    );

    // Error fallback component
    const ErrorFallback = () => (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "200px",
          backgroundColor: "#f8f8f8",
          border: "2px dashed #ccc",
          borderRadius: "8px",
          padding: "20px",
          textAlign: "center",
        }}
      >
        <div style={{ fontSize: "48px", marginBottom: "16px", color: "#666" }}>
          ⚠️
        </div>
        <p style={{ margin: "0 0 16px 0", color: "#666" }}>{fallbackText}</p>
        <button
          onClick={() => {
            setVideoError(false);
            setRetryCount(0);
            setCurrentSrcIndex(0);
            setIsLoading(true);
          }}
          style={{
            padding: "8px 16px",
            backgroundColor: "#3498db",
            color: "white",
            border: "none",
            borderRadius: "4px",
            cursor: "pointer",
          }}
        >
          Retry
        </button>
      </div>
    );

    if (!src) {
      return <ErrorFallback />;
    }

    const currentSrc = getCurrentSrc();
    const currentVideoType = detectVideoType(currentSrc);
    const processedSrc = getProcessedSrc(currentSrc, currentVideoType);
    const isEmbedType =
      currentVideoType === "youtube" ||
      currentVideoType === "linkedin" ||
      embed;

    return (
      <div className={className} style={{ width, ...style }}>
        <ConditionalDisplay condition={videoError}>
          <ErrorFallback />
        </ConditionalDisplay>

        <ConditionalDisplay
          condition={!videoError && isLoading && showLoadingSpinner}
        >
          <LoadingSpinner />
        </ConditionalDisplay>

        <ConditionalDisplay condition={!videoError && isEmbedType}>
          <iframe
            width="100%"
            height={height === "auto" ? "315" : height}
            src={processedSrc}
            title="Video player"
            style={{
              borderRadius: "8px",
              display: isLoading ? "none" : "block",
              border: "none",
            }}
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            allowFullScreen
            onLoad={handleVideoLoad}
            onError={handleVideoError}
          />
        </ConditionalDisplay>

        <ConditionalDisplay condition={!videoError && !isEmbedType}>
          <video
            ref={videoRef}
            width="100%"
            height={height}
            controls={controls}
            autoPlay={autoplay}
            muted={muted}
            preload="metadata"
            poster={poster}
            onLoadedData={handleVideoLoad}
            onError={handleVideoError}
            onEnded={handleVideoComplete}
            style={{
              borderRadius: "8px",
              display: isLoading ? "none" : "block",
            }}
          >
            <source src={processedSrc} type={type} />

            {/* Multiple format support for better browser compatibility */}
            {currentVideoType === "direct" && (
              <>
                <source
                  src={processedSrc.replace(/\.[^.]+$/, ".webm")}
                  type="video/webm"
                />
                <source
                  src={processedSrc.replace(/\.[^.]+$/, ".ogg")}
                  type="video/ogg"
                />
              </>
            )}

            {/* Alternative sources */}
            {alternativeSources.map((altSrc, index) => (
              <source key={index} src={altSrc} type={type} />
            ))}

            <p>{fallbackText}</p>
          </video>
        </ConditionalDisplay>
      </div>
    );
  }
);
VideoPlayer.displayName = "VideoPlayer";
export default VideoPlayer;
