import Image from "next/image";
import BreadCrumbs from "../../../components/UI/BreadCrumbs/BreadCrumbs";
import Backarrow from "../../../svg/metronic/back_metronic.svg";
import { PageContainer } from "../../../components/UI/Page/PageContainer/PageContainer";
import { Tab } from "../../../components/UI/Tabs/Tabs";
import CourseCard from "../../../components/LMS/CourseCard";
import style from "../index.module.css";
import TextInput from "../../../components/UI/Input/TextInput/TextInput";
import clsx from "clsx";
import { useApi } from "../../../hooks/useApi";
import { useDashboard } from "../../../hooks/useDashboard";
import { useEffect, useRef } from "react";
import { DataScroller } from "primereact/datascroller";

export default function AvailableCourses() {
  const { callApi, loading } = useApi();

  const {
    rows,
    setRows,
    totalCount,
    setTotalCount,
    lazyParams,
    globalFilter,
    onSort,
    onPage,
    onGlobalFilterChange,
    lazyParamsToQueryString,
  } = useDashboard({
    initialLazyParams: {
      first: 0,
      rows: 10,
      sortOrder: -1,
      page: 0,
      filters: {
        global: { value: "", matchMode: "contains" },
      },
    },
  });

  useEffect(() => {
    const fetchAllAvailableCourses = async () => {
      const queryString = lazyParamsToQueryString(lazyParams); // Convert lazyParams to query string
      try {
        const response = await callApi({
          method: "GET",
          url: `Courses/${queryString}`,
        });
        if (response?.data) {
          setRows(response.data.rows);
          setTotalCount(response.data.count);
        }
      } catch (error) {
        console.error("Error fetching form definition options:", error);
      }
    };

    fetchAllAvailableCourses();
  }, [lazyParams]);

  const ds = useRef(null);

  return (
    <PageContainer theme="metronic">
      <div className="flex flex-column gap-5">
        <div className="flex gap-4 mt-1">
          <Image src={Backarrow} alt="Back" />
          <BreadCrumbs
            title="Available Courses"
            breadcrumbItems={[{ label: "Available Courses" }, { label: "All" }]}
            theme="metronic"
          />
          <span
            className="p-input-icon-left mr-1"
            style={{
              display: "flex",
              width: "22vw",
            }}
          >
            <i
              className={clsx("pi pi-search", style.searchIcon)}
              style={{ top: "22px" }}
            />
            <TextInput theme="metronic" placeholder="Search Course" />
          </span>
        </div>
        <div className={style.tabContainer}>
          <div className="flex gap-4" style={{ height: "2.5rem" }}>
            <Tab
              title={"All"}
              display={true}
              theme="metronic"
              isActive={true}
            />
            <Tab title={"Computer Science"} display={true} theme="metronic" />
            <Tab title={"Soft Skills"} display={true} theme="metronic" />
          </div>
        </div>
        <div className="flex flex-wrap gap-4 justify-content-between">
          <DataScroller
            ref={ds}
            value={rows}
            itemTemplate={(data) => (
              <CourseCard
                courseTitle={"Course Title"}
                courseDescription={"Course Description"}
                assignedDate={"Assigned Date"}
              />
            )}
            rows={5}
            loader
            header="Click Load Button at Footer to Load More"
          />
        </div>
        {/* <div className="flex flex-wrap gap-4 justify-content-between">
          <CourseCard
            courseTitle={"Course Title"}
            courseDescription={"Course Description"}
            assignedDate={"Assigned Date"}
          />
          <CourseCard
            courseTitle={"Course Title"}
            courseDescription={"Course Description"}
            assignedDate={"Assigned Date"}
          />
          <CourseCard
            courseTitle={"Course Title"}
            courseDescription={"Course Description"}
            assignedDate={"Assigned Date"}
          />
        </div>
        <div className="flex flex-wrap gap-4 justify-content-between">
          <CourseCard
            courseTitle={"Course Title"}
            courseDescription={"Course Description"}
            assignedDate={"Assigned Date"}
          />
          <CourseCard
            courseTitle={"Course Title"}
            courseDescription={"Course Description"}
            assignedDate={"Assigned Date"}
          />
          <CourseCard
            courseTitle={"Course Title"}
            courseDescription={"Course Description"}
            assignedDate={"Assigned Date"}
          />
          <CourseCard
            courseTitle={"Course Title"}
            courseDescription={"Course Description"}
            assignedDate={"Assigned Date"}
          />
        </div> */}
      </div>
    </PageContainer>
  );
}
