import Image from "next/image";
import BreadCrumbs from "../../../components/UI/BreadCrumbs/BreadCrumbs";
import Backarrow from "../../../svg/metronic/back_metronic.svg";
import { PageContainer } from "../../../components/UI/Page/PageContainer/PageContainer";
import { Tab } from "../../../components/UI/Tabs/Tabs";
import CourseCard from "../../../components/LMS/CourseCard";
import style from "../index.module.css";
import TextInput from "../../../components/UI/Input/TextInput/TextInput";
import clsx from "clsx";
import { useApi } from "../../../hooks/useApi";
import { useDashboard } from "../../../hooks/useDashboard";
import { useEffect, useRef } from "react";
import { DataScroller } from "primereact/datascroller";

export default function AvailableCourses() {
  const { callApi, loading } = useApi();

  const {
    rows,
    setRows,
    totalCount,
    setTotalCount,
    lazyParams,
    globalFilter,
    onSort,
    onPage,
    onGlobalFilterChange,
    lazyParamsToQueryString,
  } = useDashboard({
    initialLazyParams: {
      first: 0,
      rows: 12,
      sortOrder: -1,
      page: 0,
      filters: {
        global: { value: "", matchMode: "contains" },
      },
    },
  });

  useEffect(() => {
    const fetchAllAvailableCourses = async () => {
      const queryString = lazyParamsToQueryString(lazyParams); // Convert lazyParams to query string
      try {
        const response = await callApi({
          method: "GET",
          url: `Courses/${queryString}`,
        });
        if (response?.data) {
          setRows(response.data.rows);
          setTotalCount(response.data.count);
        }
      } catch (error) {
        console.error("Error fetching form definition options:", error);
      }
    };

    fetchAllAvailableCourses();
  }, [lazyParams]);

  const ds = useRef(null);

  return (
    <PageContainer theme="metronic">
      <div className="flex flex-column gap-5">
        <div className="flex gap-4 mt-1">
          <Image src={Backarrow} alt="Back" />
          <BreadCrumbs
            title="Available Courses"
            breadcrumbItems={[{ label: "Available Courses" }, { label: "All" }]}
            theme="metronic"
          />
          <span
            className="p-input-icon-left mr-1"
            style={{
              display: "flex",
              width: "22vw",
            }}
          >
            <i
              className={clsx("pi pi-search", style.searchIcon)}
              style={{ top: "22px" }}
            />
            <TextInput theme="metronic" placeholder="Search Course" />
          </span>
        </div>
        <div className={style.tabContainer}>
          <div className="flex gap-4" style={{ height: "2.5rem" }}>
            <Tab
              title={"All"}
              display={true}
              theme="metronic"
              isActive={true}
            />
            <Tab title={"Computer Science"} display={true} theme="metronic" />
            <Tab title={"Soft Skills"} display={true} theme="metronic" />
          </div>
        </div>
        <DataScroller
          ref={ds}
          value={rows}
          itemTemplate={(_, index) => {
            // Group courses into rows of 4
            if (index % 4 === 0) {
              const coursesInRow = rows.slice(index, index + 4);
              return (
                <div
                  key={`row-${index}`}
                  className="flex flex-wrap gap-4 justify-content-between mb-4"
                  style={{ width: "100%" }}
                >
                  {coursesInRow.map((course, courseIndex) => (
                    <div
                      key={`course-${index + courseIndex}`}
                      style={{
                        flex: "0 0 calc(25% - 12px)",
                        minWidth: "22rem",
                        maxWidth: "22rem",
                      }}
                    >
                      <CourseCard
                        courseTitle={course?.title || "Course Title"}
                        courseDescription={
                          course?.description || "Course Description"
                        }
                        assignedDate={course?.assignedDate || "Assigned Date"}
                      />
                    </div>
                  ))}
                </div>
              );
            }
            return null;
          }}
          rows={4}
          loader
          header="Click Load Button at Footer to Load More"
        />
        {/* <div className="flex flex-wrap gap-4 justify-content-between">
          <CourseCard
            courseTitle={"Course Title"}
            courseDescription={"Course Description"}
            assignedDate={"Assigned Date"}
          />
          <CourseCard
            courseTitle={"Course Title"}
            courseDescription={"Course Description"}
            assignedDate={"Assigned Date"}
          />
          <CourseCard
            courseTitle={"Course Title"}
            courseDescription={"Course Description"}
            assignedDate={"Assigned Date"}
          />
        </div>
        <div className="flex flex-wrap gap-4 justify-content-between">
          <CourseCard
            courseTitle={"Course Title"}
            courseDescription={"Course Description"}
            assignedDate={"Assigned Date"}
          />
          <CourseCard
            courseTitle={"Course Title"}
            courseDescription={"Course Description"}
            assignedDate={"Assigned Date"}
          />
          <CourseCard
            courseTitle={"Course Title"}
            courseDescription={"Course Description"}
            assignedDate={"Assigned Date"}
          />
          <CourseCard
            courseTitle={"Course Title"}
            courseDescription={"Course Description"}
            assignedDate={"Assigned Date"}
          />
        </div> */}
      </div>
    </PageContainer>
  );
}
