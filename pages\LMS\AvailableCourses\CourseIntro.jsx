import Image from "next/image";
import BreadCrumbs from "../../../components/UI/BreadCrumbs/BreadCrumbs";
import Backarrow from "../../../svg/metronic/back_metronic.svg";
import { PageContainer } from "../../../components/UI/Page/PageContainer/PageContainer";
import style from "../index.module.css"
import TextInput from "../../../components/UI/Input/TextInput/TextInput";
import courseCover from "../../../svg/metronic/course_cover.svg";
import clsx from "clsx";
import Button from "../../../components/UI/Button/Button";
import AssessmentComplete from "../../../components/LMS/AssessmentComplete";
import { useState } from "react";
import { ConditionalDisplay } from "../../../components/UI/ConditionalDisplay/ConditionalDisplay";


export default function CourseIntro() {
  const [showAssessmentComplete, setShowAssessmentComplete] = useState(false);

  const handleEnrollClick = () => {
    setShowAssessmentComplete(true);
  };

  return (
    <PageContainer theme="metronic">
      <div className="flex flex-column gap-5">
        <div className="flex gap-4 mt-1">
          <Image src={Backarrow} alt="Back" />
          <BreadCrumbs
            title="Available Courses"
            breadcrumbItems={[
              { label: "Available Courses" }, { label: "All" }, { label: "Mechanics Part 1" }
            ]}
            theme="metronic"
          />
          <span
            className="p-input-icon-left mr-1"
            style={{
              display: "flex",
              width: "22vw"
            }}
          >
            <i
              className={clsx("pi pi-search", style.searchIcon)}
              style={{ top: "22px" }}
            />
            <TextInput theme="metronic" placeholder="Search Course" />
          </span>
        </div>
        <div
          style={{
            backgroundImage: `url(${courseCover.src})`,
            height: "29dvh",
          }}
          className={`${style.bannerBackground} w-full flex`}
        ></div>
        <ConditionalDisplay condition={!showAssessmentComplete}>
          <div className={style.pageCard}>
            <h2 className={clsx("mt-0 text-center", style.formTitle)}>
              Course Introduction : Mechanics Part 1
            </h2>
            <div className="mx-8 my-6">
              <div className={style.assessmentContainer}>
                <h3 className={style.fontText}>Learning Objectives</h3>
                <p className={style.fontText}>Mechanics is one of the oldest and most fundamental branches of physics, forming the backbone of engineering and technical applications across industries. Mechanics - part 1is designed as a foundational course that introduces learners to the core principles governing the motion and interaction of physical objects. Whether you're aiming to pursue a career in mechanical engineering, automotive technology, or general physics, this course will build the essential knowledge needed to understand how forces affect the world around us.</p>
                <p className={style.fontText}>The goal of this course is not only to teach the laws and formulas but also to help you develop a mechanical mindset: an ability to observe, analyze, and predict motion and forces in everyday situations. This critical thinking ability is vital for anyone aspiring to become a skilled mechanic, technician, or engineer.</p>
                <p className={style.fontText}>By the end of this course, you will have a strong grasp of:</p>
                <ul className={style.fontText}>
                  <li>The principles that govern how objects move and interact</li>
                  <li>The mathematical tools used to describe and predict physical behaviour</li>
                  <li>Problem-solving skills applied in technical and engineering environments</li>
                </ul>
                <p className={style.fontText}>Get ready to explore the physical forces that shape machines, vehicles, and structures - and begin building the knowledge that powers mechanical innovation.</p>
              </div>
              <div className="flex gap-4 my-5 justify-content-end">
                <Button label="Back" width="150px" theme="metronic" variant="outline" />
                <Button label="Enroll Now" width="150px" theme="metronic" onClick={handleEnrollClick} />
              </div>
            </div>
          </div>
        </ConditionalDisplay>
        <ConditionalDisplay condition={showAssessmentComplete}>
          <AssessmentComplete title="Successfully Completed!" message={<>in <strong>Mechanics Part 1.</strong> You can now access the course in the My Courses tab.<br />Start exploring and enjoy your learning journey!</>} />
        </ConditionalDisplay>

      </div>
    </PageContainer>

  )
}