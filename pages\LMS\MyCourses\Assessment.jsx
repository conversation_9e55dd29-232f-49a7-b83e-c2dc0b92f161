

import { useState } from "react";
import Image from "next/image";
import BreadCrumbs from "../../../components/UI/BreadCrumbs/BreadCrumbs";
import Backarrow from "../../../svg/metronic/back_metronic.svg";
import { PageContainer } from "../../../components/UI/Page/PageContainer/PageContainer";
import style from "../index.module.css";
import TextInput from "../../../components/UI/Input/TextInput/TextInput";
import clsx from "clsx";
import courseCover from "../../../svg/metronic/course_cover.svg";
import AssessmentCard from "../../../components/LMS/AssessmentCard";
import AssessmentComplete from "../../../components/LMS/AssessmentComplete";
import { ConditionalDisplay } from "../../../components/UI/ConditionalDisplay/ConditionalDisplay";

export default function Assessment() {
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [isAssessmentComplete, setIsAssessmentComplete] = useState(false);

  const questions = [
    {
      id: 1,
      question: "Q1: Which of the following branches of mechanics deals with objects in motion and the forces causing the motion?",
      options: ["Statics", "Kinematics", "Kinetics", "Thermodynamics"],
    },
    {
      id: 2,
      question: "Q2: Which of the following is an example of a vector quantity?",
      options: ["Speed", "Distance", "Velocity", "Mass"],
    },
    {
      id: 3,
      question: "Q3: Newton's first law is also known as?",
      options: ["Law of Inertia", "Law of Acceleration", "Law of Action-Reaction", "Law of Gravitation"],
    },
    {
      id: 4,
      question: "Q4: Which of the following is a scalar quantity?",
      options: ["Displacement", "Velocity", "Force", "Energy"],
    },
    {
      id: 5,
      question: "Q5: The rate of change of velocity is known as?",
      options: ["Speed", "Displacement", "Acceleration", "Momentum"],
    },
    {
      id: 6,
      question: "Q6: Which law states that every action has an equal and opposite reaction?",
      options: ["Newton's First Law", "Newton's Second Law", "Newton's Third Law", "Kepler's First Law"],
    },
  ];


  return (
    <PageContainer theme="metronic">
      <div className="flex flex-column gap-5">
        <div className="flex gap-4 mt-1">
          <Image src={Backarrow} alt="Back" />
          <BreadCrumbs
            title="My Course"
            breadcrumbItems={[{ label: "My Courses" }]}
            theme="metronic"
          />
          <span
            className="p-input-icon-left mr-1"
            style={{ display: "flex", width: "22vw" }}
          >
            <i
              className={clsx("pi pi-search", style.searchIcon)}
              style={{ top: "22px" }}
            />
            <TextInput theme="metronic" placeholder="Search Course" />
          </span>
        </div>

        <div
          style={{
            backgroundImage: `url(${courseCover.src})`,
            height: "29dvh",
          }}
          className={`${style.bannerBackground} w-full flex`}
        ></div>

        <div className={style.pageCard}>
          <h2 className={clsx("mt-0 text-center", style.formTitle)}>
            Assessment
          </h2>
          <ConditionalDisplay condition={!isAssessmentComplete}>
            <AssessmentCard
              questions={questions}
              currentQuestionIndex={currentQuestionIndex}
              setCurrentQuestionIndex={setCurrentQuestionIndex}
              setIsAssessmentComplete={setIsAssessmentComplete}
            /></ConditionalDisplay>
          <ConditionalDisplay condition={isAssessmentComplete}>
            <AssessmentComplete title="Assessment Completed!" message={
              <>
                The Candidate has successfully completed the Market Research skills Assessment.
                <br />
                Your certificate is now available to view and download.
              </>
            } />
            <p className={clsx("text-center", style.link)}>Download Certificate</p>
          </ConditionalDisplay>
        </div>


      </div>
    </PageContainer>
  );
}

