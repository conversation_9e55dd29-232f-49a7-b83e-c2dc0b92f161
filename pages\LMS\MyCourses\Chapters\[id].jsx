import Image from "next/image";
import BreadCrumbs from "../../../../components/UI/BreadCrumbs/BreadCrumbs";
import Backarrow from "../../../../svg/metronic/back_metronic.svg";
import { PageContainer } from "../../../../components/UI/Page/PageContainer/PageContainer";
import style from "../../index.module.css";
import TextInput from "../../../../components/UI/Input/TextInput/TextInput";
import clsx from "clsx";
import courseCover from "../../../../svg/metronic/course_cover.svg";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import PdfViewer from "../../../../components/UI/Pdf/PdfViewer";
import { useApi } from "../../../../hooks/useApi";
import { useEffect, useRef, useState } from "react";
import { ConditionalDisplay } from "../../../../components/UI/ConditionalDisplay/ConditionalDisplay";
import VideoPlayer from "../../../../components/Video/VideoPlayer";
import { useRouter } from "next/router";
import Button from "../../../../components/UI/Button/Button";

export default function Chapters({ id }) {
  const router = useRouter();
  const { callApi, loading } = useApi();
  const [chapter, setChapter] = useState(null);
  const [onVideoComplete, setOnVideoComplete] = useState(false);
  const videoRef = useRef(null);

  const fetchChapter = async () => {
    try {
      const response = await callApi({
        method: "GET",
        url: `Courses/GetChapter/${id}`,
      });
      if (response?.data) {
        setChapter(response.data);
      }
    } catch (error) {
      console.error("Error fetching chapter:", error);
    }
  };

  useEffect(() => {
    if (id) {
      fetchChapter();
    }
  }, [id]);

  const scrollToVideo = () => {
    if (videoRef.current) {
      const videoElement = videoRef.current;
      const viewportHeight = window.innerHeight;
      const videoRect = videoElement.getBoundingClientRect();

      // Calculate position to center video
      const scrollTo =
        window.pageYOffset +
        videoRect.top -
        (viewportHeight - videoRect.height) / 2;

      window.scrollTo({
        top: scrollTo,
        behavior: "smooth",
      });
    }
  };

  return (
    <PageContainer theme="metronic">
      <div className="flex flex-column gap-5">
        <div className="flex gap-4 mt-1">
          <Image
            src={Backarrow}
            alt="Back"
            className="cursor-pointer"
            onClick={() => router.back()}
          />
          <BreadCrumbs
            title="My Course"
            breadcrumbItems={[{ label: "My Courses" }]}
            theme="metronic"
          />
          <span
            className="p-input-icon-left mr-1"
            style={{ display: "flex", width: "22vw" }}
          >
            <i
              className={clsx("pi pi-search", style.searchIcon)}
              style={{ top: "22px" }}
            />
            <TextInput theme="metronic" placeholder="Search Course" />
          </span>
        </div>

        <div
          style={{
            backgroundImage: `url(${courseCover.src})`,
            height: "29dvh",
          }}
          className={`${style.bannerBackground} w-full flex`}
        ></div>

        <div className={style.pageCard}>
          <h2 className={clsx("mt-0 text-center", style.formTitle)}>
            Introduction To Mechanics
          </h2>

          <ConditionalDisplay condition={chapter?.fileType === "MP4"}>
            <section className="mt-5 flex flex-column align-items-center">
              <VideoPlayer
                ref={videoRef}
                src={chapter?.fileUrl}
                width="100%"
                height="600px"
                autoplay={true}
                muted={true}
                onLoad={scrollToVideo}
                onError={(error) =>
                  console.error("YouTube video error:", error)
                }
                onVideoComplete={() => setOnVideoComplete(true)}
              />
              <div className="mt-5 mb-5">
                <Button
                  label="Complete"
                  theme="metronic"
                  // onClick={() => router.push(`/LMS/MyCourses/Chapters/${id}`)}
                  disabled={!onVideoComplete}
                ></Button>
              </div>
            </section>
          </ConditionalDisplay>

          <ConditionalDisplay condition={chapter?.fileType === "PDF"}>
            <div className="flex flex-column align-items-center w-full">
              <PdfViewer fileUrl={chapter?.fileUrl} />
            </div>
          </ConditionalDisplay>
        </div>
      </div>
    </PageContainer>
  );
}

export async function getServerSideProps(context) {
  const { params } = context;
  const { id } = params;

  return {
    props: {
      id,
    },
  };
}
