import Image from "next/image";
import BreadCrumbs from "../../../../components/UI/BreadCrumbs/BreadCrumbs";
import Backarrow from "../../../../svg/metronic/back_metronic.svg";
import { PageContainer } from "../../../../components/UI/Page/PageContainer/PageContainer";
import style from "../../index.module.css";
import TextInput from "../../../../components/UI/Input/TextInput/TextInput";
import clsx from "clsx";
import courseCover from "../../../../svg/metronic/course_cover.svg";
import "react-pdf/dist/esm/Page/AnnotationLayer.css";
import PdfViewer from "../../../../components/UI/Pdf/PdfViewer";
import { useApi } from "../../../../hooks/useApi";
import { useEffect, useRef, useState } from "react";
import { ConditionalDisplay } from "../../../../components/UI/ConditionalDisplay/ConditionalDisplay";
import VideoPlayer from "../../../../components/Video/VideoPlayer";
import { useRouter } from "next/router";
import Button from "../../../../components/UI/Button/Button";

export default function Chapters({ id }) {
  const router = useRouter();
  const { callApi, loading } = useApi();
  const [chapter, setChapter] = useState(null);
  const [onVideoComplete, setOnVideoComplete] = useState(false);
  const videoRef = useRef(null);

  const fetchChapter = async () => {
    try {
      const response = await callApi({
        method: "GET",
        url: `Courses/GetChapter/${id}`,
      });
      if (response?.data) {
        setChapter(response.data);
      }
    } catch (error) {
      console.error("Error fetching chapter:", error);
    }
  };

  useEffect(() => {
    if (id) {
      fetchChapter();
    }
  }, [id]);

  // Scroll to video when chapter is loaded and it's a video type
  useEffect(() => {
    if (chapter?.fileType === "MP4" && chapter?.fileUrl) {
      // Wait a bit longer for the video component to render
      const timer = setTimeout(() => {
        scrollToVideo();
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [chapter]);

  const scrollToVideo = () => {
    // Add a small delay to ensure the video element is fully rendered
    setTimeout(() => {
      let targetElement = null;

      // Try to use the ref first
      if (videoRef.current) {
        targetElement = videoRef.current;
      } else {
        // Fallback to finding the video section by ID
        targetElement = document.getElementById("video-section");
      }

      if (targetElement) {
        const viewportHeight = window.innerHeight;
        const elementRect = targetElement.getBoundingClientRect();

        // Calculate position to center element in viewport
        const scrollTo =
          window.pageYOffset +
          elementRect.top -
          (viewportHeight - elementRect.height) / 2;

        window.scrollTo({
          top: Math.max(0, scrollTo), // Ensure we don't scroll to negative position
          behavior: "smooth",
        });
      }
    }, 100); // Small delay to ensure DOM is updated
  };

  return (
    <PageContainer theme="metronic">
      <div className="flex flex-column gap-5">
        <div className="flex gap-4 mt-1">
          <Image
            src={Backarrow}
            alt="Back"
            className="cursor-pointer"
            onClick={() => router.back()}
          />
          <BreadCrumbs
            title="My Course"
            breadcrumbItems={[{ label: "My Courses" }]}
            theme="metronic"
          />
          <span
            className="p-input-icon-left mr-1"
            style={{ display: "flex", width: "22vw" }}
          >
            <i
              className={clsx("pi pi-search", style.searchIcon)}
              style={{ top: "22px" }}
            />
            <TextInput theme="metronic" placeholder="Search Course" />
          </span>
        </div>

        <div
          style={{
            backgroundImage: `url(${courseCover.src})`,
            height: "29dvh",
          }}
          className={`${style.bannerBackground} w-full flex`}
        ></div>

        <div className={style.pageCard}>
          <h2 className={clsx("mt-0 text-center", style.formTitle)}>
            Introduction To Mechanics
          </h2>

          <ConditionalDisplay condition={chapter?.fileType === "MP4"}>
            <section
              className="mt-5 flex flex-column align-items-center"
              id="video-section"
            >
              <VideoPlayer
                ref={videoRef}
                src={chapter?.fileUrl}
                width="100%"
                height="600px"
                autoplay={true}
                muted={true}
                onLoad={scrollToVideo}
                onError={(error) => console.error("Video error:", error)}
                onVideoComplete={() => setOnVideoComplete(true)}
              />
              <div className="mt-5 mb-5">
                <Button
                  label="Complete"
                  theme="metronic"
                  // onClick={() => router.push(`/LMS/MyCourses/Chapters/${id}`)}
                  disabled={!onVideoComplete}
                ></Button>
              </div>
            </section>
          </ConditionalDisplay>

          <ConditionalDisplay condition={chapter?.fileType === "PDF"}>
            <div className="flex flex-column align-items-center w-full">
              <PdfViewer fileUrl={chapter?.fileUrl} />
            </div>
          </ConditionalDisplay>
        </div>
      </div>
    </PageContainer>
  );
}

export async function getServerSideProps(context) {
  const { params } = context;
  const { id } = params;

  return {
    props: {
      id,
    },
  };
}
