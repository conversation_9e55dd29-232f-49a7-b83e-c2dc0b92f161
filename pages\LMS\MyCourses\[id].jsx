import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import BreadCrumbs from "../../../components/UI/BreadCrumbs/BreadCrumbs";
import Image from "next/image";
import { PageContainer } from "../../../components/UI/Page/PageContainer/PageContainer";
import clsx from "clsx";
import style from "../index.module.css";
import eye from "../../../svg/metronic/edit_eye.svg";
import welcome from "../../../svg/metronic/welcome_course.svg";
import tick from "../../../svg/metronic/tick_button.svg";
import Backarrow from "../../../svg/metronic/back_metronic.svg";
import TextInput from "../../../components/UI/Input/TextInput/TextInput";
import Button from "../../../components/UI/Button/Button";
import { useRouter } from "next/router";
import { useContext, useEffect, useState } from "react";
import { useCourse } from "../../../hooks/LMS/Course/useCourse";
import UserProfileContext from "../../../public/UserProfileContext/UserProfileContext";

export default function Detail({ id }) {
  const router = useRouter();
  const { userID: leadUserProfileId } = useContext(UserProfileContext);

  const { course, loading, fetchCourseById } = useCourse(id, leadUserProfileId);

  useEffect(() => {
    if (id && leadUserProfileId) {
      fetchCourseById();
    }
  }, [id, leadUserProfileId]);

  const tableheader = () => (
    <div className={style.prospecTable}>
      <span className="text-xl font-bold py-2">My Lessons</span>
    </div>
  );
  const pendingHeader = () => (
    <div className={style.prospecTable}>
      <span className="text-xl font-bold py-2">Pending/Upcoming Quizzes</span>
    </div>
  );
  const statusBodyTemplate = (rowData) => (
    <div className="flex gap-4">
      <Image src={tick} alt="tick" />
      <Image
        src={eye}
        alt="eye"
        className="cursor-pointer"
        onClick={() => router.push(`/LMS/MyCourses/Chapters/${rowData?.id}`)}
      />
    </div>
  );
  const flowTemplate = (rowData) => (
    <div className="flex flex-column gap-3">
      <span>{rowData.lesson}</span>
      <span
        style={{ fontSize: "14px", fontStyle: "italic", fontWeight: "400" }}
      >
        by Steve
      </span>
    </div>
  );
  const submitTemplate = () => (
    <Button theme="metronic" variant="outline" label="Submit" width="200px" />
  );
  const mockColumns = [
    {
      field: "order",
      header: "S.No",
    },
    { field: "chapterTitle", header: "Lessons Name" },
    { field: "duration", header: "Duration" },
    {
      field: "status",
      header: "Submit",
      body: (rowData) => statusBodyTemplate(rowData),
    },
  ];
  const mockRows = [
    {
      number: "1",
      lesson: "Introduction to Mechanics",
      updatedby: "20 mins",
      status: "status",
    },
    {
      number: "2",
      lesson: "Basic Concepts and Quantities",
      updatedby: "20 mins",
      status: "status",
    },
    {
      number: "3",
      lesson: "Statics:Forces and Equilibrium",
      updatedby: "20 mins",
      status: "status",
    },
    {
      number: "3",
      lesson: "Dynamics:Motion & Newton's Laws",
      updatedby: "20 mins",
      status: "status",
    },
  ];
  const pendingRows = [
    {
      number: "1",
      lesson: "Prepare Flowchart",
      status: "status",
    },
    {
      number: "2",
      lesson: "Prepare Flowchart",
      status: "status",
    },
  ];

  return (
    <PageContainer theme="metronic">
      <div className="flex flex-column gap-5">
        <div className="flex gap-4 mt-1">
          <Image
            src={Backarrow}
            alt="Back"
            onClick={() => router.back()}
            className="cursor-pointer"
          />
          <BreadCrumbs
            title="My Course"
            breadcrumbItems={[{ label: "My Courses" }]}
            theme="metronic"
          />
          <span
            className="p-input-icon-left mr-1"
            style={{
              display: "flex",
              width: "22vw",
            }}
          >
            <i
              className={clsx("pi pi-search", style.searchIcon)}
              style={{ top: "22px" }}
            />
            <TextInput theme="metronic" placeholder="Search Course" />
          </span>
        </div>

        <div
          style={{
            backgroundImage: `url(${welcome.src})`,
            height: "29dvh",
          }}
          className={`${style.bannerBackground} w-full flex`}
        ></div>

        <DataTable
          header={tableheader}
          value={course?.chapters}
          className="custom-lead"
          loading={loading}
        >
          {mockColumns.map((col, index) => (
            <Column
              key={index}
              field={col.field}
              header={col.header}
              {...(col.body ? { body: col.body } : {})}
            />
          ))}
        </DataTable>
        <DataTable
          header={pendingHeader}
          value={pendingRows}
          className="custom-lead"
          showHeaders={false}
        >
          <Column field="number" style={{ width: "10%" }} />
          <Column field="lesson" body={flowTemplate} style={{ width: "70%" }} />
          <Column
            field="status"
            body={submitTemplate}
            style={{ width: "20%" }}
          />
        </DataTable>
      </div>
    </PageContainer>
  );
}

export async function getServerSideProps(context) {
  const { params } = context;
  const { id } = params;

  return {
    props: {
      id,
    },
  };
}
