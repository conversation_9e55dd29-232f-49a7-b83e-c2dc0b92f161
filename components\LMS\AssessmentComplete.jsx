import clsx from "clsx"
import style from "../../pages/LMS/index.module.css"
import Image from "next/image";
import complete from "../../svg/metronic/assessment_complete.svg"


const AssessmentComplete = ({ title, message }) => {
  return (
    <div className="mx-8 my-6">
      <div className={clsx("py-6", style.assessmentContainer)}>
        <div className="flex flex-column align-items-center justify-content-center">
          <Image src={complete} alt="complete" />
          <h2 className={style.fontText}>{title}</h2>
          <p className={clsx("text-center", style.fontText)}>{message}</p>
        </div>
      </div>
    </div>
  );
};

export default AssessmentComplete;